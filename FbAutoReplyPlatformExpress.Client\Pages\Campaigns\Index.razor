@page "/campaigns"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using Volo.Abp.Application.Dtos
@using System.Linq
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IAutoReplyCampaignService CampaignService
@inject IFacebookAuthService FacebookAuthService
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.View)]

<PageTitle>Auto-Reply Campaigns</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.Settings" />
                    Auto-Reply Campaigns
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                @if (HasCreatePermission)
                {
                    <Button Color="Color.Primary" Clicked="CreateCampaignAsync">
                        <Icon Name="IconName.Add" />
                        Create Campaign
                    </Button>
                }
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        <!-- Filters -->
        <Card Class="mb-3">
            <CardBody>
                <Row>
                    <Column ColumnSize="ColumnSize.Is3">
                        <Field>
                            <FieldLabel>Status</FieldLabel>
                            <Select TValue="bool?" @bind-SelectedValue="IsActiveFilter" @onchange="OnStatusFilterChanged">
                                <SelectItem TValue="bool?" Value="null">All Campaigns</SelectItem>
                                <SelectItem TValue="bool?" Value="true">Active</SelectItem>
                                <SelectItem TValue="bool?" Value="false">Inactive</SelectItem>
                            </Select>
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is5">
                        <Field>
                            <FieldLabel>Search</FieldLabel>
                            <TextEdit @bind-Text="SearchText" Placeholder="Search campaigns..." @onkeypress="OnSearchKeyPress" />
                        </Field>
                    </Column>
                    <Column ColumnSize="ColumnSize.Is2">
                        <Field>
                            <FieldLabel>&nbsp;</FieldLabel>
                            <Button Color="Color.Primary" Clicked="SearchCampaignsAsync" Block="true">
                                <Icon Name="IconName.Search" />
                                Search
                            </Button>
                        </Field>
                    </Column>
                </Row>
            </CardBody>
        </Card>

        @if (!IsConnected)
        {
            <Alert Color="Color.Warning" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                You need to connect your Facebook account first.
                <Button Color="Color.Link" Clicked="NavigateToConnection" Class="ms-2">
                    Connect Now
                </Button>
            </Alert>
        }
        else
        {
            <DataGrid @ref="dataGrid"
                TItem="AutoReplyCampaignDto"
                      Data="Campaigns?.Items"
                      ReadData="OnDataGridReadAsync"
                      TotalItems="(int)(Campaigns?.TotalCount ?? 0)"
                      ShowPager="true"
                      PageSize="PageSize"
                      Responsive="true">
                <DataGridColumns>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Field="@nameof(AutoReplyCampaignDto.CampaignName)" Caption="Campaign" Sortable="true">
                        <DisplayTemplate>
                            <div>
                                <strong>@context.CampaignName</strong>
                                @if (!string.IsNullOrEmpty(context.Description))
                                {
                                    <br /><small class="text-muted">@context.Description</small>
                                }
                                <br /><small class="text-info">@context.PageName</small>
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Field="@nameof(AutoReplyCampaignDto.PostMessage)" Caption="Post" Sortable="false">
                        <DisplayTemplate>
                            <div style="max-width: 250px;">
                                @if (!string.IsNullOrEmpty(context.PostMessage))
                                {
                                    @(context.PostMessage.Length > 80 ? context.PostMessage.Substring(0, 80) + "..." : context.PostMessage)
                                }
                                else
                                {
                                    <span class="text-muted">[No text content]</span>
                                }
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Field="@nameof(AutoReplyCampaignDto.IsActive)" Caption="Status" Sortable="true" Width="100px">
                        <DisplayTemplate>
                            @if (context.IsActive)
                            {
                                <Badge Color="Color.Success">Active</Badge>
                            }
                            else
                            {
                                <Badge Color="Color.Secondary">Inactive</Badge>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Caption="Reply Types" Sortable="false" Width="120px">
                        <DisplayTemplate>
                            <div>
                                @if (context.SendPublicReply)
                                {
                                    <Badge Color="Color.Info" Class="me-1">Public</Badge>
                                }
                                @if (context.SendPrivateReply)
                                {
                                    <Badge Color="Color.Warning">Private</Badge>
                                }
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Caption="Statistics" Sortable="false" Width="120px">
                        <DisplayTemplate>
                            <div>
                                <small>Total: @context.TotalRepliesSent</small><br />
                                <small>Public: @context.PublicRepliesSent</small><br />
                                <small>Private: @context.PrivateRepliesSent</small>
                            </div>
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Field="@nameof(AutoReplyCampaignDto.LastReplyAt)" Caption="Last Reply" Sortable="true" Width="120px">
                        <DisplayTemplate>
                            @if (context.LastReplyAt.HasValue)
                            {
                                @context.LastReplyAt.Value.ToString("MMM dd, yyyy")
                                <br />
                                <small class="text-muted">@context.LastReplyAt.Value.ToString("HH:mm")</small>
                            }
                            else
                            {
                                <span class="text-muted">Never</span>
                            }
                        </DisplayTemplate>
                    </DataGridColumn>
                    <DataGridColumn TItem="AutoReplyCampaignDto" Caption="Actions" Sortable="false" Width="200px">
                        <DisplayTemplate>
                            <Dropdown>
                                <DropdownToggle Color="Color.Primary" Size="Size.Small">
                                    Actions
                                </DropdownToggle>
                                <DropdownMenu>
                                    <DropdownItem Clicked="() => ViewActivitiesAsync(context.Id)">
                                        <Icon Name="IconName.List" />
                                        View Activities
                                    </DropdownItem>
                                    @if (HasEditPermission)
                                    {
                                        <DropdownItem Clicked="() => EditCampaignAsync(context.Id)">
                                            <Icon Name="IconName.Edit" />
                                            Edit
                                        </DropdownItem>
                                    }
                                    @if (HasActivatePermission && !context.IsActive)
                                    {
                                        <DropdownItem Clicked="() => ActivateCampaignAsync(context.Id)">
                                            <Icon Name="IconName.Play" />
                                            Activate
                                        </DropdownItem>
                                    }
                                    @if (HasDeactivatePermission && context.IsActive)
                                    {
                                        <DropdownItem Clicked="() => DeactivateCampaignAsync(context.Id)">
                                            <Icon Name="IconName.Pause" />
                                            Deactivate
                                        </DropdownItem>
                                    }
                                    @if (HasDeletePermission)
                                    {
                                        <DropdownDivider />
                                        <DropdownItem Clicked="() => DeleteCampaignAsync(context.Id)" Class="text-danger">
                                            <Icon Name="IconName.Delete" />
                                            Delete
                                        </DropdownItem>
                                    }
                                </DropdownMenu>
                            </Dropdown>
                        </DisplayTemplate>
                    </DataGridColumn>
                </DataGridColumns>
                <LoadingTemplate>
                    <div class="text-center p-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading campaigns...</p>
                    </div>
                </LoadingTemplate>
                <EmptyTemplate>
                    <Alert Color="Color.Info" Visible="true" Class="mt-3">
                        <Icon Name="IconName.Info" />
                        No campaigns found.
                        @if (HasCreatePermission)
                        {
                            <text>Click "Create Campaign" to get started.</text>
                        }
                    </Alert>
                </EmptyTemplate>
            </DataGrid>
        }
    </CardBody>
</Card>

@code {
    private DataGrid<AutoReplyCampaignDto> dataGrid;
    private PagedResultDto<AutoReplyCampaignDto>? Campaigns;
    private bool IsLoading = false;
    private bool IsConnected = false;
    private int PageSize = 10;
    private int CurrentPage = 1;
    private string CurrentSorting = "";

    // Permissions
    private bool HasCreatePermission = false;
    private bool HasEditPermission = false;
    private bool HasDeletePermission = false;
    private bool HasActivatePermission = false;
    private bool HasDeactivatePermission = false;

    // Filters
    private bool? IsActiveFilter;
    private string SearchText = "";

    [Parameter]
    [SupplyParameterFromQuery]
    public Guid? PostId { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadPermissions();
        IsConnected = await FacebookAuthService.IsConnectedToFacebookAsync();
    }

    private async Task LoadPermissions()
    {
        HasCreatePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Campaigns.Create);
        HasEditPermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Campaigns.Edit);
        HasDeletePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Campaigns.Delete);
        HasActivatePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Campaigns.Activate);
        HasDeactivatePermission = await AuthorizationService.IsGrantedAsync(FbAutoReplyPlatformExpressPermissions.Campaigns.Deactivate);
    }

    private async Task CheckConnectionAndLoadCampaigns()
    {
        try
        {
            IsConnected = await FacebookAuthService.IsConnectedToFacebookAsync();
            if (IsConnected)
            {
                await LoadCampaigns();
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error checking Facebook connection: {ex.Message}");
        }
    }

    private async Task LoadCampaigns()
    {
        if (!IsConnected) return;

        IsLoading = true;
        await InvokeAsync(StateHasChanged);

        try
        {

            var request = new GetCampaignsInput
            {
                MaxResultCount = PageSize,
                SkipCount = (CurrentPage - 1) * PageSize,
                Sorting = CurrentSorting,
                FacebookPostId = PostId,
                IsActive = IsActiveFilter,
                SearchText = SearchText
            };

            Campaigns = await CampaignService.GetListAsync(request);
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading campaigns: {ex.Message}");
            Campaigns ??= new PagedResultDto<AutoReplyCampaignDto>();

        }
        finally
        {
            IsLoading = false;
            await InvokeAsync(StateHasChanged);

        }
    }

    private async Task OnDataGridReadAsync(DataGridReadDataEventArgs<AutoReplyCampaignDto> e)
    {
        CurrentPage = e.Page;
        PageSize = e.PageSize;
        CurrentSorting = string.Join(",", e.Columns
            .Where(c => c.SortDirection != SortDirection.Default)
            .Select(c => c.Field + (c.SortDirection == SortDirection.Descending ? " DESC" : "")));

        await LoadCampaigns();
    }

    private async Task OnStatusFilterChanged(ChangeEventArgs e)
    {
        CurrentPage = 1;
        await dataGrid.Reload();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await SearchCampaignsAsync();
        }
    }

    private async Task SearchCampaignsAsync()
    {
        CurrentPage = 1;
        await dataGrid.Reload();
    }

    private void NavigateToConnection()
    {
        NavigationManager.NavigateTo("/facebook/connection");
    }

    private void CreateCampaignAsync()
    {
        NavigationManager.NavigateTo("/campaigns/select-post");
    }

    private void EditCampaignAsync(Guid campaignId)
    {
        NavigationManager.NavigateTo($"/campaigns/edit/{campaignId}");
    }

    private void ViewActivitiesAsync(Guid campaignId)
    {
        NavigationManager.NavigateTo($"/campaigns/{campaignId}/activities");
    }

    private async Task ActivateCampaignAsync(Guid campaignId)
    {
        try
        {
            await CampaignService.ActivateAsync(campaignId);
            await Message.Success("Campaign activated successfully!");
            await dataGrid.Reload();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error activating campaign: {ex.Message}");
        }
    }

    private async Task DeactivateCampaignAsync(Guid campaignId)
    {
        try
        {
            await CampaignService.DeactivateAsync(campaignId);
            await Message.Success("Campaign deactivated successfully!");
            await dataGrid.Reload();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error deactivating campaign: {ex.Message}");
        }
    }

    private async Task DeleteCampaignAsync(Guid campaignId)
    {
        var confirmed = await Message.Confirm("Are you sure you want to delete this campaign? This action cannot be undone.");
        if (confirmed)
        {
            try
            {
                await CampaignService.DeleteAsync(campaignId);
                await Message.Success("Campaign deleted successfully!");
                await dataGrid.Reload();
            }
            catch (Exception ex)
            {
                await Message.Error($"Error deleting campaign: {ex.Message}");
            }
        }
    }
}
