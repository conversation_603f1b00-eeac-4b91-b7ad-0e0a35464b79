using System;
using System.Linq;
using System.Threading.Tasks;
using FbAutoReplyPlatformExpress.Entities;
using FbAutoReplyPlatformExpress.Permissions;
using FbAutoReplyPlatformExpress.Services.Dtos;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Users;

namespace FbAutoReplyPlatformExpress.Services;

[Authorize]
public class AutoReplyCampaignService : ApplicationService, IAutoReplyCampaignService
{
    private readonly IRepository<AutoReplyCampaign, Guid> _campaignRepository;
    private readonly IRepository<FacebookPost, Guid> _postRepository;
    private readonly IRepository<FacebookPage, Guid> _pageRepository;
    private readonly IRepository<FacebookUser, Guid> _facebookUserRepository;
    private readonly IRepository<CampaignActivity, Guid> _activityRepository;
    private readonly ILogger<AutoReplyCampaignService> _logger;

    public AutoReplyCampaignService(
        IRepository<AutoReplyCampaign, Guid> campaignRepository,
        IRepository<FacebookPost, Guid> postRepository,
        IRepository<FacebookPage, Guid> pageRepository,
        IRepository<FacebookUser, Guid> facebookUserRepository,
        IRepository<CampaignActivity, Guid> activityRepository,
        ILogger<AutoReplyCampaignService> logger)
    {
        _campaignRepository = campaignRepository;
        _postRepository = postRepository;
        _pageRepository = pageRepository;
        _facebookUserRepository = facebookUserRepository;
        _activityRepository = activityRepository;
        _logger = logger;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.View)]
    public async Task<PagedResultDto<AutoReplyCampaignDto>> GetListAsync(GetCampaignsInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _campaignRepository.GetQueryableAsync();
        var postQueryable = await _postRepository.GetQueryableAsync();
        var pageQueryable = await _pageRepository.GetQueryableAsync();

        var query = from campaign in queryable
                    join post in postQueryable on campaign.FacebookPostId equals post.Id
                    join page in pageQueryable on post.FacebookPageId equals page.Id
                    where page.FacebookUserId == facebookUser.Id
                    select new { Campaign = campaign, Post = post, Page = page };

        // Apply filters
        if (input.FacebookPostId.HasValue)
        {
            query = query.Where(x => x.Campaign.FacebookPostId == input.FacebookPostId.Value);
        }

        if (!string.IsNullOrEmpty(input.SearchText))
        {
            query = query.Where(x => x.Campaign.CampaignName.Contains(input.SearchText) ||
                                   x.Post.Message.Contains(input.SearchText) ||
                                   x.Page.PageName.Contains(input.SearchText));
        }

        if (input.IsActive.HasValue)
        {
            query = query.Where(x => x.Campaign.IsActive == input.IsActive.Value);
        }

        if (input.FromDate.HasValue)
        {
            query = query.Where(x => x.Campaign.CreationTime >= input.FromDate.Value);
        }

        if (input.ToDate.HasValue)
        {
            query = query.Where(x => x.Campaign.CreationTime <= input.ToDate.Value);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            // For now, use default sorting. Dynamic sorting would require System.Linq.Dynamic.Core
            query = query.OrderByDescending(x => x.Campaign.CreationTime);
        }
        else
        {
            query = query.OrderByDescending(x => x.Campaign.CreationTime);
        }

        query = query.Skip(input.SkipCount).Take(input.MaxResultCount);

        var items = await AsyncExecuter.ToListAsync(query);
        var campaignDtos = items.Select(item =>
        {
            var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(item.Campaign);
            campaignDto.PostMessage = item.Post.Message;
            campaignDto.PageName = item.Page.PageName;
            return campaignDto;
        }).ToList();

        return new PagedResultDto<AutoReplyCampaignDto>(totalCount, campaignDtos);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.View)]
    public async Task<AutoReplyCampaignDto> GetAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = post.Message;
        campaignDto.PageName = page.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Create)]
    public async Task<AutoReplyCampaignDto> CreateAsync(CreateAutoReplyCampaignDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var post = await _postRepository.GetAsync(input.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        // Check if there's already an active campaign for this post
        var existingCampaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostId == input.FacebookPostId && c.IsActive);

        if (existingCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        var campaign = new AutoReplyCampaign(
            GuidGenerator.Create(),
            input.FacebookPostId,
            input.CampaignName,
            input.PublicReplyMessage);

        if (!string.IsNullOrEmpty(input.Description))
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.PrivateReplyMessage = input.PrivateReplyMessage;
        }

        campaign.ConfigureReplyTypes(input.SendPublicReply, input.SendPrivateReply);
        campaign.SetDateRange(input.StartDate, input.EndDate);
        campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser);

        await _campaignRepository.InsertAsync(campaign);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = post.Message;
        campaignDto.PageName = page.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Create)]
    public async Task<AutoReplyCampaignDto> CreateFromPostAsync(CreateCampaignFromPostDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        // Validate that the user has access to the Facebook page
        var pageQuery = await _pageRepository.GetQueryableAsync();
        var page = await AsyncExecuter.FirstOrDefaultAsync(
            pageQuery.Where(p => p.FacebookPageId == input.FacebookPageId && p.FacebookUserId == facebookUser.Id));

        if (page == null)
        {
            throw new UnauthorizedAccessException("You don't have access to this Facebook page.");
        }

        // Check if there's already an active campaign for this post
        var campaignQuery = await _campaignRepository.GetQueryableAsync();
        var existingCampaign = await AsyncExecuter.FirstOrDefaultAsync(
            campaignQuery.Where(c => c.FacebookPostIdString == input.FacebookPostId && c.IsActive));

        if (existingCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        var campaign = new AutoReplyCampaign(
            GuidGenerator.Create(),
            input.FacebookPostId,
            input.FacebookPageId,
            input.PageName,
            input.PostContent,
            input.PostCreatedTime,
            input.CampaignName,
            input.PublicReplyMessage);

        if (!string.IsNullOrEmpty(input.Description))
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.PrivateReplyMessage = input.PrivateReplyMessage;
        }

        campaign.ConfigureReplyTypes(input.SendPublicReply, input.SendPrivateReply);
        campaign.SetDateRange(input.StartDate, input.EndDate);
        campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser);
        campaign.UpdatePostMetadata(input.PostPermalinkUrl, input.PostPictureUrl);

        await _campaignRepository.InsertAsync(campaign);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = input.PostContent;
        campaignDto.PageName = input.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Edit)]
    public async Task<AutoReplyCampaignDto> UpdateAsync(Guid id, UpdateAutoReplyCampaignDto input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        if (!string.IsNullOrEmpty(input.CampaignName))
        {
            campaign.CampaignName = input.CampaignName;
        }

        if (input.Description != null)
        {
            campaign.Description = input.Description;
        }

        if (!string.IsNullOrEmpty(input.PublicReplyMessage) || !string.IsNullOrEmpty(input.PrivateReplyMessage))
        {
            campaign.UpdateMessages(
                input.PublicReplyMessage ?? campaign.PublicReplyMessage,
                input.PrivateReplyMessage);
        }

        if (input.SendPublicReply.HasValue || input.SendPrivateReply.HasValue)
        {
            campaign.ConfigureReplyTypes(
                input.SendPublicReply ?? campaign.SendPublicReply,
                input.SendPrivateReply ?? campaign.SendPrivateReply);
        }

        if (input.StartDate.HasValue || input.EndDate.HasValue)
        {
            campaign.SetDateRange(
                input.StartDate ?? campaign.StartDate,
                input.EndDate ?? campaign.EndDate);
        }

        if (input.MaxRepliesPerUser.HasValue)
        {
            campaign.SetMaxRepliesPerUser(input.MaxRepliesPerUser.Value);
        }

        await _campaignRepository.UpdateAsync(campaign);

        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = post.Message;
        campaignDto.PageName = page.PageName;

        return campaignDto;
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Delete)]
    public async Task DeleteAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        await _campaignRepository.DeleteAsync(campaign);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Activate)]
    public async Task<AutoReplyCampaignDto> ActivateAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        // Check if there's already an active campaign for this post
        var existingActiveCampaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostId == campaign.FacebookPostId && c.IsActive && c.Id != id);

        if (existingActiveCampaign != null)
        {
            throw new UserFriendlyException("There is already an active campaign for this post. Please deactivate it first.");
        }

        campaign.Activate();
        await _campaignRepository.UpdateAsync(campaign);

        return await GetAsync(id);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Deactivate)]
    public async Task<AutoReplyCampaignDto> DeactivateAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var campaign = await _campaignRepository.GetAsync(id);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        campaign.Deactivate();
        await _campaignRepository.UpdateAsync(campaign);

        return await GetAsync(id);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities)]
    public async Task<PagedResultDto<CampaignActivityDto>> GetActivitiesAsync(GetActivitiesInput input)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();

        var queryable = await _activityRepository.GetQueryableAsync();
        var campaignQueryable = await _campaignRepository.GetQueryableAsync();
        var postQueryable = await _postRepository.GetQueryableAsync();
        var pageQueryable = await _pageRepository.GetQueryableAsync();

        var query = from activity in queryable
                    join campaign in campaignQueryable on activity.CampaignId equals campaign.Id
                    join post in postQueryable on campaign.FacebookPostId equals post.Id
                    join page in pageQueryable on post.FacebookPageId equals page.Id
                    where page.FacebookUserId == facebookUser.Id
                    select new { Activity = activity, Campaign = campaign, Post = post, Page = page };

        // Apply filters
        if (input.CampaignId.HasValue)
        {
            query = query.Where(x => x.Activity.CampaignId == input.CampaignId.Value);
        }

        if (!string.IsNullOrEmpty(input.SearchText))
        {
            query = query.Where(x => x.Activity.CommenterName.Contains(input.SearchText) ||
                                   x.Activity.OriginalComment.Contains(input.SearchText) ||
                                   x.Campaign.CampaignName.Contains(input.SearchText));
        }

        if (input.HasError.HasValue)
        {
            query = query.Where(x => x.Activity.HasError == input.HasError.Value);
        }

        if (input.PublicReplySent.HasValue)
        {
            query = query.Where(x => x.Activity.PublicReplySent == input.PublicReplySent.Value);
        }

        if (input.PrivateReplySent.HasValue)
        {
            query = query.Where(x => x.Activity.PrivateReplySent == input.PrivateReplySent.Value);
        }

        if (input.FromDate.HasValue)
        {
            query = query.Where(x => x.Activity.CommentCreatedAt >= input.FromDate.Value);
        }

        if (input.ToDate.HasValue)
        {
            query = query.Where(x => x.Activity.CommentCreatedAt <= input.ToDate.Value);
        }

        var totalCount = await AsyncExecuter.CountAsync(query);

        // Apply sorting
        if (!string.IsNullOrEmpty(input.Sorting))
        {
            // For now, use default sorting. Dynamic sorting would require System.Linq.Dynamic.Core
            query = query.OrderByDescending(x => x.Activity.CommentCreatedAt);
        }
        else
        {
            query = query.OrderByDescending(x => x.Activity.CommentCreatedAt);
        }

        query = query.Skip(input.SkipCount).Take(input.MaxResultCount);

        var items = await AsyncExecuter.ToListAsync(query);
        var activityDtos = items.Select(item =>
        {
            var activityDto = ObjectMapper.Map<CampaignActivity, CampaignActivityDto>(item.Activity);
            activityDto.CampaignName = item.Campaign.CampaignName;
            activityDto.PostMessage = item.Post.Message;
            activityDto.PageName = item.Page.PageName;
            return activityDto;
        }).ToList();

        return new PagedResultDto<CampaignActivityDto>(totalCount, activityDtos);
    }

    [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.ViewActivities)]
    public async Task<CampaignActivityDto> GetActivityAsync(Guid id)
    {
        var facebookUser = await GetCurrentUserFacebookUserAsync();
        var activity = await _activityRepository.GetAsync(id);
        var campaign = await _campaignRepository.GetAsync(activity.CampaignId);
        var post = await _postRepository.GetAsync(campaign.FacebookPostId);
        var page = await _pageRepository.GetAsync(post.FacebookPageId);

        if (page.FacebookUserId != facebookUser.Id)
        {
            throw new UnauthorizedAccessException();
        }

        var activityDto = ObjectMapper.Map<CampaignActivity, CampaignActivityDto>(activity);
        activityDto.CampaignName = campaign.CampaignName;
        activityDto.PostMessage = post.Message;
        activityDto.PageName = page.PageName;

        return activityDto;
    }

    public async Task<AutoReplyCampaignDto?> GetActiveCampaignForPostAsync(string facebookPostId)
    {
        var post = await _postRepository.FirstOrDefaultAsync(p => p.FacebookPostId == facebookPostId);
        if (post == null)
        {
            return null;
        }

        var campaign = await _campaignRepository.FirstOrDefaultAsync(c =>
            c.FacebookPostId == post.Id && c.IsActive && c.IsValidForReply());

        if (campaign == null)
        {
            return null;
        }

        var page = await _pageRepository.GetAsync(post.FacebookPageId);
        var campaignDto = ObjectMapper.Map<AutoReplyCampaign, AutoReplyCampaignDto>(campaign);
        campaignDto.PostMessage = post.Message;
        campaignDto.PageName = page.PageName;

        return campaignDto;
    }

    private async Task<FacebookUser> GetCurrentUserFacebookUserAsync()
    {
        var currentUserId = CurrentUser.GetId();
        var facebookUser = await _facebookUserRepository.FirstOrDefaultAsync(x => x.UserId == currentUserId && x.IsActive);

        if (facebookUser == null)
        {
            throw new UserFriendlyException("Facebook account not connected. Please connect your Facebook account first.");
        }

        return facebookUser;
    }
}
