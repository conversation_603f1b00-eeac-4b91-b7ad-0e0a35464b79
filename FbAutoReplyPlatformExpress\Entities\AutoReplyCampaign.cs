using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp.Domain.Entities.Auditing;

namespace FbAutoReplyPlatformExpress.Entities;

public class AutoReplyCampaign : FullAuditedAggregateRoot<Guid>
{
    [Required]
    public Guid FacebookPostId { get; set; }

    // Minimal post metadata stored directly in campaign
    [Required]
    [StringLength(256)]
    public string FacebookPostIdString { get; set; } = string.Empty;

    [Required]
    [StringLength(256)]
    public string FacebookPageIdString { get; set; } = string.Empty;

    [Required]
    [StringLength(256)]
    public string PageName { get; set; } = string.Empty;

    [StringLength(2048)]
    public string PostContent { get; set; } = string.Empty;

    [StringLength(1024)]
    public string? PostPermalinkUrl { get; set; }

    [StringLength(1024)]
    public string? PostPictureUrl { get; set; }

    public DateTime PostCreatedTime { get; set; }

    [Required]
    [StringLength(256)]
    public string CampaignName { get; set; } = string.Empty;

    [StringLength(1024)]
    public string? Description { get; set; }

    [Required]
    [StringLength(2048)]
    public string PublicReplyMessage { get; set; } = string.Empty;

    [StringLength(2048)]
    public string? PrivateReplyMessage { get; set; }

    public bool IsActive { get; set; } = true;

    public bool SendPublicReply { get; set; } = true;

    public bool SendPrivateReply { get; set; } = false;

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public int MaxRepliesPerUser { get; set; } = 1; // Limit replies per user to avoid spam

    public int TotalRepliesSent { get; set; } = 0;

    public int PublicRepliesSent { get; set; } = 0;

    public int PrivateRepliesSent { get; set; } = 0;

    public DateTime? LastReplyAt { get; set; }

    // Navigation property
    public virtual FacebookPost FacebookPost { get; set; } = null!;

    protected AutoReplyCampaign()
    {
        // For EF Core
    }

    public AutoReplyCampaign(
        Guid id,
        Guid facebookPostId,
        string campaignName,
        string publicReplyMessage) : base(id)
    {
        FacebookPostId = facebookPostId;
        CampaignName = campaignName;
        PublicReplyMessage = publicReplyMessage;
        IsActive = true;
        SendPublicReply = true;
        SendPrivateReply = false;
        MaxRepliesPerUser = 1;
        TotalRepliesSent = 0;
        PublicRepliesSent = 0;
        PrivateRepliesSent = 0;
    }

    // Constructor for creating campaigns from in-memory posts
    public AutoReplyCampaign(
        Guid id,
        string facebookPostId,
        string facebookPageId,
        string pageName,
        string postContent,
        DateTime postCreatedTime,
        string campaignName,
        string publicReplyMessage) : base(id)
    {
        FacebookPostId = Guid.NewGuid(); // Generate a new GUID for backward compatibility
        FacebookPostIdString = facebookPostId;
        FacebookPageIdString = facebookPageId;
        PageName = pageName;
        PostContent = postContent;
        PostCreatedTime = postCreatedTime;
        CampaignName = campaignName;
        PublicReplyMessage = publicReplyMessage;
        IsActive = true;
        SendPublicReply = true;
        SendPrivateReply = false;
        MaxRepliesPerUser = 1;
        TotalRepliesSent = 0;
        PublicRepliesSent = 0;
        PrivateRepliesSent = 0;
    }

    public void UpdateMessages(string publicReplyMessage, string? privateReplyMessage = null)
    {
        PublicReplyMessage = publicReplyMessage;
        if (!string.IsNullOrEmpty(privateReplyMessage))
        {
            PrivateReplyMessage = privateReplyMessage;
        }
    }

    public void ConfigureReplyTypes(bool sendPublicReply, bool sendPrivateReply)
    {
        SendPublicReply = sendPublicReply;
        SendPrivateReply = sendPrivateReply;
    }

    public void SetDateRange(DateTime? startDate, DateTime? endDate)
    {
        StartDate = startDate;
        EndDate = endDate;
    }

    public void SetMaxRepliesPerUser(int maxReplies)
    {
        if (maxReplies > 0)
        {
            MaxRepliesPerUser = maxReplies;
        }
    }

    public void IncrementReplyCount(bool isPublicReply)
    {
        TotalRepliesSent++;
        if (isPublicReply)
        {
            PublicRepliesSent++;
        }
        else
        {
            PrivateRepliesSent++;
        }
        LastReplyAt = DateTime.UtcNow;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }

    public bool IsValidForReply()
    {
        if (!IsActive) return false;

        var now = DateTime.UtcNow;

        if (StartDate.HasValue && now < StartDate.Value) return false;
        if (EndDate.HasValue && now > EndDate.Value) return false;

        return true;
    }

    public void UpdatePostMetadata(string? permalinkUrl, string? pictureUrl)
    {
        PostPermalinkUrl = permalinkUrl;
        PostPictureUrl = pictureUrl;
    }
}
