﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace FbAutoReplyPlatformExpress.Migrations
{
    /// <inheritdoc />
    public partial class AddCampaignPostMetadata : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppAutoReplyCampaigns_AppFacebookPosts_FacebookPostId",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.AddColumn<string>(
                name: "FacebookPageIdString",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "FacebookPostIdString",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PageName",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(256)",
                maxLength: 256,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "PostContent",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(2048)",
                maxLength: 2048,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<DateTime>(
                name: "PostCreatedTime",
                table: "AppAutoReplyCampaigns",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "PostPermalinkUrl",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PostPictureUrl",
                table: "AppAutoReplyCampaigns",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_AppAutoReplyCampaigns_FacebookPostIdString",
                table: "AppAutoReplyCampaigns",
                column: "FacebookPostIdString");

            migrationBuilder.AddForeignKey(
                name: "FK_AppAutoReplyCampaigns_AppFacebookPosts_FacebookPostId",
                table: "AppAutoReplyCampaigns",
                column: "FacebookPostId",
                principalTable: "AppFacebookPosts",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AppAutoReplyCampaigns_AppFacebookPosts_FacebookPostId",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropIndex(
                name: "IX_AppAutoReplyCampaigns_FacebookPostIdString",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "FacebookPageIdString",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "FacebookPostIdString",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PageName",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PostContent",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PostCreatedTime",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PostPermalinkUrl",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.DropColumn(
                name: "PostPictureUrl",
                table: "AppAutoReplyCampaigns");

            migrationBuilder.AddForeignKey(
                name: "FK_AppAutoReplyCampaigns_AppFacebookPosts_FacebookPostId",
                table: "AppAutoReplyCampaigns",
                column: "FacebookPostId",
                principalTable: "AppFacebookPosts",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
