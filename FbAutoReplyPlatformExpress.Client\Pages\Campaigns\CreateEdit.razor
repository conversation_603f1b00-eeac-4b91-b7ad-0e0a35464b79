@page "/campaigns/create"
@page "/campaigns/edit/{Id:guid}"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using FbAutoReplyPlatformExpress.Permissions
@using Microsoft.AspNetCore.Authorization
@using System.ComponentModel.DataAnnotations
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IAutoReplyCampaignService CampaignService
@inject IFacebookPostService PostService
@inject NavigationManager NavigationManager
@attribute [Authorize(FbAutoReplyPlatformExpressPermissions.Campaigns.Create)]

<PageTitle>@(IsEditMode ? "Edit Campaign" : "Create Campaign")</PageTitle>

<Card>
    <CardHeader>
        <Row Class="justify-content-between">
            <Column ColumnSize="ColumnSize.IsAuto">
                <h2>
                    <Icon Name="IconName.Settings" />
                    @(IsEditMode ? "Edit Campaign" : "Create Auto-Reply Campaign")
                </h2>
            </Column>
            <Column ColumnSize="ColumnSize.IsAuto">
                <Button Color="Color.Secondary" Clicked="NavigateBack">
                    <Icon Name="IconName.ArrowLeft" />
                    Back
                </Button>
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        @if (IsLoading)
        {
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">@(IsEditMode ? "Loading campaign..." : "Loading post information...")</p>
            </div>
        }
        else if (SelectedPost != null)
        {
            <!-- Post Information -->
            <Card Class="mb-4">
                <CardHeader>
                    <h5>Selected Post</h5>
                </CardHeader>
                <CardBody>
                    <Row>
                        <Column ColumnSize="ColumnSize.Is8">
                            <strong>@SelectedPost.PageName</strong>
                            <p class="mt-2">@(string.IsNullOrEmpty(SelectedPost.Message) ? "[No text content]" : SelectedPost.Message)</p>
                            <small class="text-muted">
                                Posted: @SelectedPost.FacebookCreatedTime.ToString("MMM dd, yyyy HH:mm") • 
                                Type: @SelectedPost.PostType • 
                                👍 @SelectedPost.LikesCount • 
                                💬 @SelectedPost.CommentsCount • 
                                🔄 @SelectedPost.SharesCount
                            </small>
                        </Column>
                    </Row>
                </CardBody>
            </Card>

            <!-- Campaign Form -->
            <Validations @ref="ValidationsRef" Model="@Campaign" ValidateOnLoad="false">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Campaign Configuration</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Validation>
                                            <Field>
                                                <FieldLabel>Campaign Name *</FieldLabel>
                                                <TextEdit @bind-Text="@Campaign.CampaignName" Placeholder="Enter campaign name">
                                                    <Feedback>
                                                        <ValidationError />
                                                    </Feedback>
                                                </TextEdit>
                                            </Field>
                                        </Validation>
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>Max Replies Per User</FieldLabel>
                                            <NumericEdit @bind-Value="@Campaign.MaxRepliesPerUser" Min="1" Max="10" />
                                            <FieldHelp>Maximum number of auto-replies per user (1-10)</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <Field>
                                            <FieldLabel>Description</FieldLabel>
                                            <MemoEdit @bind-Text="@Campaign.Description" Rows="2" Placeholder="Optional campaign description" />
                                        </Field>
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>Start Date</FieldLabel>
                                            <DateEdit @bind-Date="@Campaign.StartDate" />
                                            <FieldHelp>Leave empty to start immediately</FieldHelp>
                                        </Field>
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>End Date</FieldLabel>
                                            <DateEdit @bind-Date="@Campaign.EndDate" />
                                            <FieldHelp>Leave empty for no end date</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>
                            </CardBody>
                        </Card>
                    </Column>
                </Row>

                <Row Class="mt-3">
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Reply Configuration</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <Field>
                                            <Check @bind-Checked="@Campaign.SendPublicReply">Send Public Reply</Check>
                                            <FieldHelp>Reply publicly to comments (visible to everyone)</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                @if (Campaign.SendPublicReply)
                                {
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is12">
                                            <Validation>
                                                <Field>
                                                    <FieldLabel>Public Reply Message *</FieldLabel>
                                                    <MemoEdit @bind-Text="@Campaign.PublicReplyMessage" Rows="3" Placeholder="Enter your public reply message">
                                                        <Feedback>
                                                            <ValidationError />
                                                        </Feedback>
                                                    </MemoEdit>
                                                    <FieldHelp>This message will be posted as a public comment reply</FieldHelp>
                                                </Field>
                                            </Validation>
                                        </Column>
                                    </Row>
                                }

                                <Row Class="mt-3">
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <Field>
                                            <Check @bind-Checked="@Campaign.SendPrivateReply">Send Private Message</Check>
                                            <FieldHelp>Send a private message to the commenter</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                @if (Campaign.SendPrivateReply)
                                {
                                    <Row>
                                        <Column ColumnSize="ColumnSize.Is12">
                                            <Field>
                                                <FieldLabel>Private Message</FieldLabel>
                                                <MemoEdit @bind-Text="@Campaign.PrivateReplyMessage" Rows="3" Placeholder="Enter your private message" />
                                                <FieldHelp>This message will be sent as a private message</FieldHelp>
                                            </Field>
                                        </Column>
                                    </Row>
                                }

                                @if (!Campaign.SendPublicReply && !Campaign.SendPrivateReply)
                                {
                                    <Alert Color="Color.Warning" Visible="true">
                                        <Icon Name="IconName.ExclamationTriangle" />
                                        Please select at least one reply type (Public Reply or Private Message).
                                    </Alert>
                                }
                            </CardBody>
                        </Card>
                    </Column>
                </Row>

                <Row Class="mt-4">
                    <Column ColumnSize="ColumnSize.Is12">
                        <div class="d-flex justify-content-between">
                            <Button Color="Color.Secondary" Clicked="NavigateBack">
                                Cancel
                            </Button>
                            <div>
                                @if (IsEditMode)
                                {
                                    <Button Color="Color.Warning" Clicked="SaveAsDraftAsync" Class="me-2" Disabled="@IsSaving">
                                        <Icon Name="IconName.Save" />
                                        Save as Draft
                                    </Button>
                                }
                                <Button Color="Color.Success" Clicked="SaveAndActivateAsync" Disabled="@(!CanSave || IsSaving)">
                                    @if (IsSaving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    }
                                    <Icon Name="IconName.CheckCircle" />
                                    @(IsEditMode ? "Save & Activate" : "Create & Activate")
                                </Button>
                            </div>
                        </div>
                    </Column>
                </Row>
            </Validations>
        }
        else
        {
            <Alert Color="Color.Danger" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                Post not found or you don't have permission to access it.
            </Alert>
        }
    </CardBody>
</Card>

@code {
    [Parameter] public Guid? Id { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public Guid? PostId { get; set; }

    private bool IsEditMode => Id.HasValue;
    private bool IsLoading = false;
    private bool IsSaving = false;
    private FacebookPostDto? SelectedPost;
    private CampaignFormModel Campaign = new();
    private Validations? ValidationsRef;

    private bool CanSave => 
        !string.IsNullOrWhiteSpace(Campaign.CampaignName) &&
        (Campaign.SendPublicReply || Campaign.SendPrivateReply) &&
        (!Campaign.SendPublicReply || !string.IsNullOrWhiteSpace(Campaign.PublicReplyMessage));

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            IsLoading = true;

            if (IsEditMode && Id.HasValue)
            {
                // Load existing campaign
                var existingCampaign = await CampaignService.GetAsync(Id.Value);
                var post = await PostService.GetAsync(existingCampaign.FacebookPostId);
                
                SelectedPost = post;
                Campaign = new CampaignFormModel
                {
                    CampaignName = existingCampaign.CampaignName,
                    Description = existingCampaign.Description,
                    PublicReplyMessage = existingCampaign.PublicReplyMessage,
                    PrivateReplyMessage = existingCampaign.PrivateReplyMessage,
                    SendPublicReply = existingCampaign.SendPublicReply,
                    SendPrivateReply = existingCampaign.SendPrivateReply,
                    StartDate = existingCampaign.StartDate,
                    EndDate = existingCampaign.EndDate,
                    MaxRepliesPerUser = existingCampaign.MaxRepliesPerUser
                };
            }
            else if (PostId.HasValue)
            {
                // Load post for new campaign
                SelectedPost = await PostService.GetAsync(PostId.Value);
                Campaign = new CampaignFormModel
                {
                    CampaignName = $"Auto-reply for {SelectedPost.PageName} post",
                    SendPublicReply = true,
                    SendPrivateReply = false,
                    MaxRepliesPerUser = 1
                };
            }
        }
        catch (Exception ex)
        {
            await Message.Error($"Error loading data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task SaveAsDraftAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;
            
            if (IsEditMode && Id.HasValue)
            {
                var updateDto = CreateUpdateDto();
                await CampaignService.UpdateAsync(Id.Value, updateDto);
                await Message.Success("Campaign saved successfully!");
            }
            else
            {
                var createDto = CreateCreateDto();
                await CampaignService.CreateAsync(createDto);
                await Message.Success("Campaign created successfully!");
            }

            NavigateBack();
        }
        catch (Exception ex)
        {
            await Message.Error($"Error saving campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task SaveAndActivateAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;
            
            AutoReplyCampaignDto campaign;
            
            if (IsEditMode && Id.HasValue)
            {
                var updateDto = CreateUpdateDto();
                campaign = await CampaignService.UpdateAsync(Id.Value, updateDto);
                campaign = await CampaignService.ActivateAsync(campaign.Id);
                await Message.Success("Campaign updated and activated successfully!");
            }
            else
            {
                var createDto = CreateCreateDto();
                campaign = await CampaignService.CreateAsync(createDto);
                campaign = await CampaignService.ActivateAsync(campaign.Id);
                await Message.Success("Campaign created and activated successfully!");
            }

            NavigationManager.NavigateTo($"/campaigns");
        }
        catch (Exception ex)
        {
            await Message.Error($"Error saving and activating campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task<bool> ValidateForm()
    {
        if (ValidationsRef != null)
        {
            return await ValidationsRef.ValidateAll();
        }
        return false;
    }

    private CreateAutoReplyCampaignDto CreateCreateDto()
    {
        return new CreateAutoReplyCampaignDto
        {
            FacebookPostId = SelectedPost!.Id,
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            StartDate = Campaign.StartDate,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser
        };
    }

    private UpdateAutoReplyCampaignDto CreateUpdateDto()
    {
        return new UpdateAutoReplyCampaignDto
        {
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            StartDate = Campaign.StartDate,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser
        };
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns");
    }

    public class CampaignFormModel
    {
        [Required]
        [StringLength(256)]
        public string CampaignName { get; set; } = "";

        public string? Description { get; set; }

        [Required]
        public string PublicReplyMessage { get; set; } = "";

        public string? PrivateReplyMessage { get; set; }

        public bool SendPublicReply { get; set; } = true;

        public bool SendPrivateReply { get; set; } = false;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [Range(1, 10)]
        public int MaxRepliesPerUser { get; set; } = 1;
    }
}
