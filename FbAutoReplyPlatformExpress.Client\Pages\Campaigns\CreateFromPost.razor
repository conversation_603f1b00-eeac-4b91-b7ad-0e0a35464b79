@page "/campaigns/create-from-post"
@using FbAutoReplyPlatformExpress.Services
@using FbAutoReplyPlatformExpress.Services.Dtos
@using System.ComponentModel.DataAnnotations
@using Volo.Abp.AspNetCore.Components.Messages
@using Volo.Abp.AspNetCore.Components.Web
@using Microsoft.JSInterop
@inherits FbAutoReplyPlatformExpressComponentBase
@inject IAutoReplyCampaignService CampaignService
@inject IUiMessageService UiMessage
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<Card>
    <CardHeader>
        <Row>
            <Column ColumnSize="ColumnSize.Is8">
                <h4>
                    <Icon Name="IconName.Add" />
                    Create Auto-Reply Campaign
                </h4>
                <p class="text-muted mb-0">Configure your auto-reply campaign for the selected Facebook post</p>
            </Column>
            <Column ColumnSize="ColumnSize.Is4" Class="text-end">
                <Button Color="Color.Secondary" Clicked="NavigateBack">
                    <Icon Name="IconName.ArrowLeft" />
                    Back to Post Selection
                </Button>
            </Column>
        </Row>
    </CardHeader>
    <CardBody>
        @if (IsLoading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
        }
        else if (SelectedPost == null)
        {
            <Alert Color="Color.Warning" Visible="true">
                <Icon Name="IconName.ExclamationTriangle" />
                <strong>No Post Selected</strong>
                <p class="mb-0">Please select a Facebook post first.</p>
                <Button Color="Color.Primary" Class="mt-2" Clicked="@(() => NavigationManager.NavigateTo("/campaigns/select-post"))">
                    Select Post
                </Button>
            </Alert>
        }
        else
        {
            <!-- Selected Post Preview -->
            <Card Class="mb-4">
                <CardHeader>
                    <h6 class="mb-0">Selected Facebook Post</h6>
                </CardHeader>
                <CardBody>
                    <Row>
                        @if (!string.IsNullOrEmpty(SelectedPost.PostPictureUrl))
                        {
                            <Column ColumnSize="ColumnSize.Is3">
                                <img src="@SelectedPost.PostPictureUrl" 
                                     alt="Post image" 
                                     class="img-fluid rounded" 
                                     style="max-height: 150px; width: 100%; object-fit: cover;" />
                            </Column>
                        }
                        <Column ColumnSize="@(string.IsNullOrEmpty(SelectedPost.PostPictureUrl) ? ColumnSize.Is12 : ColumnSize.Is9)">
                            <div class="d-flex align-items-center mb-2">
                                <strong class="text-primary">@SelectedPost.PageName</strong>
                                <small class="text-muted ms-2">@SelectedPost.PostCreatedTime.ToString("MMM dd, yyyy")</small>
                            </div>
                            @if (!string.IsNullOrEmpty(SelectedPost.PostContent))
                            {
                                <p class="mb-2">@SelectedPost.PostContent</p>
                            }
                            @if (!string.IsNullOrEmpty(SelectedPost.PostPermalinkUrl))
                            {
                                <Button Color="Color.Link" Size="Size.Small" Clicked="() => OpenFacebookPostAsync(SelectedPost.PostPermalinkUrl!)">
                                    <Icon Name="IconName.Link" />
                                    View on Facebook
                                </Button>
                            }
                        </Column>
                    </Row>
                </CardBody>
            </Card>

            <!-- Campaign Form -->
            <Validations @ref="ValidationsRef" Model="@Campaign" ValidateOnLoad="false">
                <Row>
                    <Column ColumnSize="ColumnSize.Is12">
                        <Card>
                            <CardHeader>
                                <h5>Campaign Configuration</h5>
                            </CardHeader>
                            <CardBody>
                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Validation>
                                            <Field>
                                                <FieldLabel>Campaign Name *</FieldLabel>
                                                <TextEdit @bind-Text="@Campaign.CampaignName" Placeholder="Enter campaign name">
                                                    <Feedback>
                                                        <ValidationError />
                                                    </Feedback>
                                                </TextEdit>
                                            </Field>
                                        </Validation>
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>Max Replies Per User</FieldLabel>
                                            <NumericEdit @bind-Value="@Campaign.MaxRepliesPerUser" Min="1" Max="10" />
                                            <FieldHelp>Maximum number of auto-replies per user (1-10)</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <Field>
                                            <FieldLabel>Description</FieldLabel>
                                            <MemoEdit @bind-Text="@Campaign.Description" Rows="3" Placeholder="Optional campaign description" />
                                        </Field>
                                    </Column>
                                </Row>

                                <!-- Reply Configuration -->
                                <Row Class="mt-4">
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <h6>Reply Configuration</h6>
                                        <hr />
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check TValue="bool" @bind-Checked="@Campaign.SendPublicReply">
                                                Send Public Reply
                                            </Check>
                                            <FieldHelp>Reply publicly to comments on the post</FieldHelp>
                                        </Field>
                                        @if (Campaign.SendPublicReply)
                                        {
                                            <Validation>
                                                <Field>
                                                    <FieldLabel>Public Reply Message *</FieldLabel>
                                                    <MemoEdit @bind-Text="@Campaign.PublicReplyMessage" Rows="4" Placeholder="Enter your public reply message">
                                                        <Feedback>
                                                            <ValidationError />
                                                        </Feedback>
                                                    </MemoEdit>
                                                </Field>
                                            </Validation>
                                        }
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <Check TValue="bool" @bind-Checked="@Campaign.SendPrivateReply">
                                                Send Private Message
                                            </Check>
                                            <FieldHelp>Send a private message to commenters</FieldHelp>
                                        </Field>
                                        @if (Campaign.SendPrivateReply)
                                        {
                                            <Field>
                                                <FieldLabel>Private Message</FieldLabel>
                                                <MemoEdit @bind-Text="@Campaign.PrivateReplyMessage" Rows="4" Placeholder="Enter your private message" />
                                            </Field>
                                        }
                                    </Column>
                                </Row>

                                <!-- Date Range -->
                                <Row Class="mt-4">
                                    <Column ColumnSize="ColumnSize.Is12">
                                        <h6>Campaign Schedule (Optional)</h6>
                                        <hr />
                                    </Column>
                                </Row>

                                <Row>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>Start Date</FieldLabel>
                                            <DateEdit TValue="DateTime?" @bind-Date="@Campaign.StartDate" />
                                            <FieldHelp>Leave empty to start immediately</FieldHelp>
                                        </Field>
                                    </Column>
                                    <Column ColumnSize="ColumnSize.Is6">
                                        <Field>
                                            <FieldLabel>End Date</FieldLabel>
                                            <DateEdit TValue="DateTime?" @bind-Date="@Campaign.EndDate" />
                                            <FieldHelp>Leave empty for no end date</FieldHelp>
                                        </Field>
                                    </Column>
                                </Row>
                            </CardBody>
                        </Card>
                    </Column>
                </Row>

                <Row Class="mt-4">
                    <Column ColumnSize="ColumnSize.Is12">
                        <div class="d-flex justify-content-between">
                            <Button Color="Color.Secondary" Clicked="NavigateBack">
                                Cancel
                            </Button>
                            <div>
                                <Button Color="Color.Warning" Clicked="SaveAsDraftAsync" Class="me-2" Disabled="@IsSaving">
                                    <Icon Name="IconName.Save" />
                                    Save as Draft
                                </Button>
                                <Button Color="Color.Success" Clicked="SaveAndActivateAsync" Disabled="@(!CanSave || IsSaving)">
                                    @if (IsSaving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    }
                                    <Icon Name="IconName.CheckCircle" />
                                    Create & Activate
                                </Button>
                            </div>
                        </div>
                    </Column>
                </Row>
            </Validations>
        }
    </CardBody>
</Card>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostId { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PageId { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PageName { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostContent { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostPermalinkUrl { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostPictureUrl { get; set; }

    [Parameter]
    [SupplyParameterFromQuery]
    public string? PostCreatedTime { get; set; }

    private bool IsLoading = false;
    private bool IsSaving = false;
    private SelectedPostInfo? SelectedPost;
    private CampaignFormModel Campaign = new();
    private Validations? ValidationsRef;

    private bool CanSave => 
        !string.IsNullOrWhiteSpace(Campaign.CampaignName) &&
        (Campaign.SendPublicReply || Campaign.SendPrivateReply) &&
        (!Campaign.SendPublicReply || !string.IsNullOrWhiteSpace(Campaign.PublicReplyMessage));

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        try
        {
            IsLoading = true;

            if (string.IsNullOrEmpty(PostId) || string.IsNullOrEmpty(PageId))
            {
                return;
            }

            SelectedPost = new SelectedPostInfo
            {
                FacebookPostId = PostId,
                FacebookPageId = PageId,
                PageName = PageName ?? "Unknown Page",
                PostContent = PostContent ?? string.Empty,
                PostPermalinkUrl = PostPermalinkUrl,
                PostPictureUrl = PostPictureUrl,
                PostCreatedTime = DateTime.TryParse(PostCreatedTime, out var createdTime) ? createdTime : DateTime.Now
            };

            Campaign = new CampaignFormModel
            {
                CampaignName = $"Auto-reply for {SelectedPost.PageName} post",
                SendPublicReply = true,
                SendPrivateReply = false,
                MaxRepliesPerUser = 1
            };
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error loading data: {ex.Message}");
        }
        finally
        {
            IsLoading = false;
        }
    }

    private async Task SaveAsDraftAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;
            
            var createDto = CreateDto();
            var campaign = await CampaignService.CreateFromPostAsync(createDto);
            await UiMessage.Success("Campaign created successfully!");

            NavigationManager.NavigateTo("/campaigns");
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error saving campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task SaveAndActivateAsync()
    {
        if (!await ValidateForm()) return;

        try
        {
            IsSaving = true;
            
            var createDto = CreateDto();
            var campaign = await CampaignService.CreateFromPostAsync(createDto);
            campaign = await CampaignService.ActivateAsync(campaign.Id);
            await UiMessage.Success("Campaign created and activated successfully!");

            NavigationManager.NavigateTo("/campaigns");
        }
        catch (Exception ex)
        {
            await UiMessage.Error($"Error saving campaign: {ex.Message}");
        }
        finally
        {
            IsSaving = false;
        }
    }

    private async Task<bool> ValidateForm()
    {
        if (ValidationsRef != null)
        {
            return await ValidationsRef.ValidateAll();
        }
        return false;
    }

    private CreateCampaignFromPostDto CreateDto()
    {
        return new CreateCampaignFromPostDto
        {
            FacebookPostId = SelectedPost!.FacebookPostId,
            FacebookPageId = SelectedPost.FacebookPageId,
            PageName = SelectedPost.PageName,
            PostContent = SelectedPost.PostContent,
            PostPermalinkUrl = SelectedPost.PostPermalinkUrl,
            PostPictureUrl = SelectedPost.PostPictureUrl,
            PostCreatedTime = SelectedPost.PostCreatedTime,
            CampaignName = Campaign.CampaignName,
            Description = Campaign.Description,
            PublicReplyMessage = Campaign.PublicReplyMessage,
            PrivateReplyMessage = Campaign.PrivateReplyMessage,
            SendPublicReply = Campaign.SendPublicReply,
            SendPrivateReply = Campaign.SendPrivateReply,
            StartDate = Campaign.StartDate,
            EndDate = Campaign.EndDate,
            MaxRepliesPerUser = Campaign.MaxRepliesPerUser
        };
    }

    private async Task OpenFacebookPostAsync(string permalinkUrl)
    {
        await JSRuntime.InvokeVoidAsync("open", permalinkUrl, "_blank");
    }

    private void NavigateBack()
    {
        NavigationManager.NavigateTo("/campaigns/select-post");
    }

    public class SelectedPostInfo
    {
        public string FacebookPostId { get; set; } = string.Empty;
        public string FacebookPageId { get; set; } = string.Empty;
        public string PageName { get; set; } = string.Empty;
        public string PostContent { get; set; } = string.Empty;
        public string? PostPermalinkUrl { get; set; }
        public string? PostPictureUrl { get; set; }
        public DateTime PostCreatedTime { get; set; }
    }

    public class CampaignFormModel
    {
        [Required]
        [StringLength(256)]
        public string CampaignName { get; set; } = "";

        public string? Description { get; set; }

        [Required]
        public string PublicReplyMessage { get; set; } = "";

        public string? PrivateReplyMessage { get; set; }

        public bool SendPublicReply { get; set; } = true;

        public bool SendPrivateReply { get; set; } = false;

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        [Range(1, 10)]
        public int MaxRepliesPerUser { get; set; } = 1;
    }
}
