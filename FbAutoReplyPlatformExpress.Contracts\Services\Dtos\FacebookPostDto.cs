using System;
using Volo.Abp.Application.Dtos;

namespace FbAutoReplyPlatformExpress.Services.Dtos;

public class FacebookPostDto : FullAuditedEntityDto<Guid>
{
    public string FacebookPostId { get; set; } = string.Empty;
    public Guid FacebookPageId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PostType { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public string? PermalinkUrl { get; set; }
    public string? PictureUrl { get; set; }
    public string? FullPictureUrl { get; set; }
    public DateTime FacebookCreatedTime { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
    public int SharesCount { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastSyncAt { get; set; }

    // Additional properties for UI
    public string PageName { get; set; } = string.Empty;
    public bool HasActiveCampaign { get; set; }
}

public class CreateFacebookPostDto
{
    public string FacebookPostId { get; set; } = string.Empty;
    public Guid FacebookPageId { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PostType { get; set; } = string.Empty;
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public string? PermalinkUrl { get; set; }
    public string? PictureUrl { get; set; }
    public string? FullPictureUrl { get; set; }
    public DateTime FacebookCreatedTime { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
    public int SharesCount { get; set; }
}

public class UpdateFacebookPostDto
{
    public string? Message { get; set; }
    public string? AttachmentUrl { get; set; }
    public string? LinkUrl { get; set; }
    public int? LikesCount { get; set; }
    public int? CommentsCount { get; set; }
    public int? SharesCount { get; set; }
}

public class GetPostsInput : PagedAndSortedResultRequestDto
{
    public Guid? FacebookPageId { get; set; }
    public string? SearchText { get; set; }
    public bool? HasActiveCampaign { get; set; }
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

// DTOs for in-memory post selection workflow
public class FacebookPostSelectionDto
{
    public string FacebookPostId { get; set; } = string.Empty;
    public string FacebookPageId { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
    public string PageProfilePictureUrl { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? PermalinkUrl { get; set; }
    public string? PictureUrl { get; set; }
    public string? FullPictureUrl { get; set; }
    public DateTime FacebookCreatedTime { get; set; }
    public int LikesCount { get; set; }
    public int CommentsCount { get; set; }
    public int SharesCount { get; set; }
    public bool HasActiveCampaign { get; set; }
}

public class CreateCampaignFromPostDto
{
    public string FacebookPostId { get; set; } = string.Empty;
    public string FacebookPageId { get; set; } = string.Empty;
    public string PageName { get; set; } = string.Empty;
    public string PostContent { get; set; } = string.Empty;
    public string? PostPermalinkUrl { get; set; }
    public string? PostPictureUrl { get; set; }
    public DateTime PostCreatedTime { get; set; }

    // Campaign configuration
    public string CampaignName { get; set; } = string.Empty;
    public string? Description { get; set; }
    public string PublicReplyMessage { get; set; } = string.Empty;
    public string? PrivateReplyMessage { get; set; }
    public bool SendPublicReply { get; set; } = true;
    public bool SendPrivateReply { get; set; } = false;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int MaxRepliesPerUser { get; set; } = 1;
}

public class GetPostsForSelectionInput
{
    public int Limit { get; set; } = 100;
    public Guid? FacebookPageId { get; set; }
}
